version: "3.7"

services:
  lendivo_website:
    build:
      args:
        user: ${APP_USER}
        uid: ${APP_USER_ID}
      context: ./
      dockerfile: ./docker/php/Dockerfile
    image: ${APP_NAME}_image
    container_name: ${APP_NAME}
    restart: always
    working_dir: /var/www/
    volumes:
      - ~/.ssh:/home/<USER>/.ssh
      - ./src:/var/www
    environment:
      TZ: ${APP_TZ}
    depends_on:
      - redis
    networks:
      - ${APP_NAME}_network

  nginx:
    image: nginx:alpine
    container_name: ${APP_NAME}_nginx
    restart: always
    environment :
      TZ: ${APP_TZ}
    ports:
      - ${NGINX_PORT}:80
      - ${NGINX_PORT_SSL}:443
    volumes:
      - ./src:/var/www
      - ./docker/nginx/server.crt:/etc/ssl/certs/nginx-selfsigned.crt
      - ./docker/nginx/server.key:/etc/ssl/private/nginx-selfsigned.key
      - ./docker/nginx:/etc/nginx/conf.d/
    depends_on:
      - lendivo_website
    networks:
      - ${APP_NAME}_network

  redis:
    image: redis:latest
    container_name: ${APP_NAME}_redis
    restart: always
    command: redis-server --port 6399
    ports:
      - ${REDIS_PORT}:6379
    volumes:
      - ./data/redis_data:/data
    networks:
      - ${APP_NAME}_network

networks:
  lendivo_website_network:
    driver: bridge
