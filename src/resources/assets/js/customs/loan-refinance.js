import axios from "axios";

export default class LoanRefinance {
    constructor() {
        this.reInitActiveElements();
    }

    init() {
        this.setProductTabWhoCanRefinanceLoan();
        this.reInitActiveElements();
        this.calcRefinanceAmount();

        document.querySelectorAll('li.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                this.reInitActiveElements();
                this.calcRefinanceAmount();
            })
        });

        document
            .querySelector('button#refinance-submit')
            .addEventListener('click', () => {
                this.submitRefinanceRequest();
            });
    }

    submitRefinanceRequest() {
        let $paymentMethodId = document.querySelector('input[name="payment_method_id"]:checked').value;
        if (parseInt($paymentMethodId) === 1) {
            $('input[name="iban"]').parsley().validate();

            if (!$('input[name="iban"]').parsley().isValid()) {
                console.log($('input[name="iban"]').parsley().isValid());
                return false;
            }
        }

        const submitBtn = document.querySelector('button#refinance-submit');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.style.pointerEvents = "none";
        }

        axios
            .post('/new-loan', {
                product_id: this.productId,
                amount_requested: this.amountRequested.value,
                period_requested: this.period,
                payment_method_id: $paymentMethodId,
                iban: document.querySelector('input[name="iban"]').value,
                insurance: this.insurance
            })
            .then(resp => {
                if (!resp.data.success && !resp.data?.redirectTo) {

                    let alertHeading = document.querySelector('h5.alert-heading');
                    let alertDanger = document.querySelector('.alert.alert-danger');

                    if (!alertHeading) {
                        alertDanger = document.createElement('div');
                        alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none');

                        alertDanger.innerHTML = '<h5 class="alert-heading">Нещо не е наред!</h5><p class="mb-0 error-message"></p><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';

                        alertHeading = alertDanger.querySelector('h5.alert-heading');

                        document
                            .querySelector('div#credit-info')
                            ?.prepend(alertDanger);
                    }

                    if (resp.data.messages && resp.data.messages.length > 0) {
                        alertHeading.textContent = resp.data.messages;
                    }
                    document.querySelector('div.alert-danger').classList.remove('d-none');

                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.style.pointerEvents = "auto";
                    }
                }

                /// when session expired redirect to home
                if (!resp.data.success && resp.data?.redirectTo) {
                    window.location.replace(resp.data.redirectTo);
                }

                if (resp.data.success) {
                    window.location.replace(resp.data.redirectTo);
                }
            })
            .catch(error => {
                let $errors = "";
                Object.values(error.response?.data.errors).forEach(function (row) {
                    $errors += row[0] + "\n";
                });

                let alertDanger = document.createElement('div');
                alertDanger.classList.add('alert', 'alert-danger', 'alert-dismissible', 'fade', 'show', 'p-2', 'd-none', 'errors-container');
                document
                    .querySelector('div#credit-info')
                    ?.prepend(alertDanger);

                $('div.errors-container').html($errors);
                $('div.errors-container').removeClass('d-none');

                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.style.pointerEvents = "auto";
                }
            });
    }

    reInitActiveElements() {
        this.currentNavLinkLi = document.querySelector('li.nav-item > a.nav-link.active').parentNode;
        this.currentNavLink = document.querySelector('a.nav-link.active');
        this.currentNavTab = document.querySelector('div.tab-pane.active');
        this.amountRequested = this.currentNavTab.querySelector('input[name="amount_requested"]');
        this.periodRequested = this.currentNavTab.querySelector('input[name="period_requested"]');
        this.productId = this.currentNavTab.querySelector('input[name="product_id"]').value;
        this.insurance = this.currentNavTab.querySelector('input[type="radio"][name="insurance"]:checked')?.value || 'without_insurance';
        this.period = null;
        if (this.currentNavTab.querySelector('input[name="period_requested"]')) {
            this.period = this.currentNavTab.querySelector('input[name="period_requested"]').value;
        }

        this.maxCurrentTabAmount = parseInt(this.amountRequested.getAttribute('max'));
    }

    updateSefFormData() {
        let $sefForm = document.querySelector('form[name="sef_document"]');

        $sefForm
            .querySelector('input[name="amount_requested"]')
            .setAttribute('value', this.amountRequested.value);

        $sefForm
            .querySelector('input[name="product_id"]')
            .setAttribute('value', this.productId);

        if (this.period) {
            $sefForm
                .querySelector('input[name="period_requested"]')
                .setAttribute('value', this.period);
        }
    }

    calcRefinanceAmount() {
        let amount = (window.refinanceSettings.currentAmount + 50);
        if (window.refinanceSettings.currentAmount >= this.maxCurrentTabAmount) {
            amount = this.maxCurrentTabAmount;
        }

        // this.amountRequested.setAttribute('min', amount);
        // this.amountRequested.setAttribute('value', amount);
        // $('#amount-range-' + this.productId + '-value').text(amount);

        this.fetchRefinanceData();
        this.amountRequested.addEventListener('change', () => {
            this.fetchRefinanceData();
        })

        if (this.periodRequested) {
            this.periodRequested.addEventListener('change', () => {
                this.period = this.currentNavTab.querySelector('input[name="period_requested"]').value;

                this.fetchRefinanceData();
            })
        }
    }

    fetchRefinanceData() {
        axios
            .post('/refinance-calc', {
                amount: this.amountRequested.value,
                product_id: this.productId,
                period: this.period,
            })
            .then(resp => {
                if (resp.data.success === true) {
                    let data = resp.data.response;

                    $('span.new-installment-date').text(data.installment_due_date);
                    $('span.period-label').text(data.period + ' ' + data.period_label);

                    $('span.new-loan-amount').text(data.amount);
                    $('span.amount-to-receive').text(data.amount_received);
                    $('span.installment-amount').text(data.installment_amount);

                    $('span.new-loan-amount-eur').text(data.amount_eur);
                    $('span.amount-to-receive-eur').text(data.amount_received_eur);
                    $('span.installment-amount-eur').text(data.installment_amount_eur);

                    this.updateSefFormData();

                    document.querySelector('button#refinance-submit').removeAttribute('disabled');
                }

                if (resp.data.message === 'Unauthenticated.') {
                    axios
                        .get('/logout')
                        .then(resp => {
                            location.replace('/vhod-v-profil');
                        });
                }
            })
            .catch(error => {
                console.log(error);
            })
    }

    setProductTabWhoCanRefinanceLoan() {
        if (window.refinanceSettings.currentAmount >= this.maxCurrentTabAmount) {
            // this.currentNavLink.classList.add('disabled');
            // this.currentNavLinkLi.nextElementSibling.querySelector('a.nav-link').click();
        }
    }
};

$(function () {
    if (window.refinanceSettings !== undefined) {
        const refinance = new LoanRefinance();
        refinance.init();
    }
});
