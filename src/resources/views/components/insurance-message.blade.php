@php
    $startAmount = (int) config('app.insurance_start_amount');

    $showClass = 'd-none';
    $checkedByDefault = false;
    if (
        isset($productData['slider_data']['default_amount'])
        && (int) $productData['slider_data']['default_amount'] > (int) config('app.insurance_start_amount')
    ) {
        $showClass = '';
        $checkedByDefault = true;
    }
@endphp
<div id="insurance-{{$productId}}" class="{{$showClass}} text-start">
    <div class="p-3 mt-3 mb-3" style="border:1px solid #cccccc; border-radius: 6px">
        <!-- Toggle Header -->
        <p>
            <span class="cursor-pointer" id="insuranceToggle-{{$productId}}">
                <input type="radio"
                       name="insurance"
                       value="with_insurance"
                       class="me-2"
                       id="with-insurance-{{$productId}}"
                       @if(isset($checkedByDefault) && true === $checkedByDefault)
                           checked="checked"
                       @endif
                />
                &nbsp;Защита срещу финансова нестабилност
                <span class="ms-2">
                    <i class="fa-solid fa-chevron-right arrow" id="insuranceArrow-{{$productId}}"></i>
                </span>
            </span>
        </p>

        <!-- Collapsible Content -->
        <div id="insuranceContent-{{$productId}}" class="d-none">
            <div class="ps-4 mt-3">
                <p>
                    <label for="from_loan-{{$productId}}" class="cursor-pointer">
                        <input type="radio"
                               name="pay_type"
                               value="with_insurance_from_loan"
                               id="from_loan-{{$productId}}"
                               @if(isset($checkedByDefault) && true === $checkedByDefault)
                                   checked="checked"
                               @endif
                        />&nbsp;
                        Желая сумата на застрахователната премия да бъде платена от размера на кредита.
                    </label>
                </p>

                <p>
                    <label for="pay-bank-{{$productId}}" class="text-secondary cursor-pointer">
                        <input type="radio"
                               name="pay_type"
                               id="pay-bank-{{$productId}}"
                               disabled
                        />&nbsp;
                        Желая да платя застрахователната премия по банкова сметка.
                    </label>
                </p>
            </div>
            <p class="text-secondary">
                <label for="without-insurance-{{$productId}}" class="cursor-pointer">
                    <input type="radio"
                           name="insurance"
                           value="without_insurance"
                           class="me-2"
                           id="without-insurance-{{$productId}}"
                    />
                    &nbsp;Без защита срещу финансова нестабилност.
                </label>
            </p>
        </div>
    </div>
</div>

@once
    @push('scripts')
        <script type="text/javascript">
            $(() => {
                let lastChoice = {};

                document.querySelectorAll('[id^="insurance-"]').forEach(function (insuranceBox) {
                    const productId = insuranceBox.id.replace('insurance-', '');

                    const inputRadioWithInsurance = insuranceBox.querySelector('input[name="insurance"][value="with_insurance"]');
                    const inputRadioWithoutInsurance = insuranceBox.querySelector('input[name="insurance"][value="without_insurance"]');
                    const inputRadioPayType = insuranceBox.querySelector('input[name="pay_type"][value="with_insurance_from_loan"]');
                    const allPayTypeRadios = insuranceBox.querySelectorAll('input[name="pay_type"]');

                    const amountInput = document.getElementById(`amount-range-${productId}`);

                    // Toggle functionality
                    const toggleButton = document.getElementById(`insuranceToggle-${productId}`);
                    const toggleContent = document.getElementById(`insuranceContent-${productId}`);
                    const toggleArrow = document.getElementById(`insuranceArrow-${productId}`);

                    if (toggleButton && toggleContent && toggleArrow) {
                        toggleArrow.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            if (toggleContent.classList.contains('d-none')) {
                                toggleContent.classList.remove('d-none');
                                toggleArrow.classList.remove('fa-chevron-right');
                                toggleArrow.classList.add('fa-chevron-down');
                            } else {
                                toggleContent.classList.add('d-none');
                                toggleArrow.classList.remove('fa-chevron-down');
                                toggleArrow.classList.add('fa-chevron-right');
                            }
                        });
                    }

                    if (inputRadioWithInsurance && inputRadioWithInsurance.checked) {
                        lastChoice[productId] = 'with_insurance';
                    } else if (inputRadioWithoutInsurance && inputRadioWithoutInsurance.checked) {
                        lastChoice[productId] = 'without_insurance';
                    }
                    if (inputRadioPayType && inputRadioPayType.checked) {
                        lastChoice['pay_type_' + productId] = 'from_loan';
                    }

                    insuranceBox.querySelectorAll('input[name="insurance"]').forEach(radio => {
                        radio.addEventListener('change', function (event) {
                            lastChoice[productId] = event.target.value;

                            // If user selects "without insurance", uncheck any pay_type
                            if (event.target.value === 'without_insurance') {
                                allPayTypeRadios.forEach(payTypeRadio => {
                                    payTypeRadio.checked = false;
                                });
                            }
                        });
                    });

                    insuranceBox.querySelectorAll('input[name="pay_type"]').forEach(radio => {
                        radio.addEventListener('change', function (event) {
                            if (event.target.checked) {
                                lastChoice['pay_type_' + productId] = event.target.value;
                                // Automatically check "with_insurance" if a pay type is selected
                                if (inputRadioWithInsurance && !inputRadioWithInsurance.checked) {
                                    inputRadioWithInsurance.checked = true;
                                    lastChoice[productId] = 'with_insurance';
                                }
                            }
                        });
                    });

                    if (amountInput) {
                        amountInput.addEventListener('change', function (event) {
                            const targetAmount = parseInt(event.target.value);

                            if (targetAmount >= {{$startAmount}}) {
                                insuranceBox.classList.remove('d-none');

                                if (lastChoice[productId] === 'with_insurance' && inputRadioWithInsurance) {
                                    inputRadioWithInsurance.checked = true;
                                    if (lastChoice['pay_type_' + productId] && inputRadioPayType) {
                                        inputRadioPayType.checked = true;
                                    }
                                } else if (lastChoice[productId] === 'without_insurance' && inputRadioWithoutInsurance) {
                                    inputRadioWithoutInsurance.checked = true;
                                } else {
                                    // If no choice was made, default to "with insurance"
                                    if (inputRadioWithInsurance) {
                                        inputRadioWithInsurance.checked = true;
                                        inputRadioPayType.checked = true;
                                        lastChoice[productId] = 'with_insurance';
                                        lastChoice['pay_type_' + productId] = 'from_loan';
                                    }
                                }
                            } else {
                                // Save the current state before hiding
                                if (inputRadioWithInsurance && inputRadioWithInsurance.checked) {
                                     lastChoice[productId] = 'with_insurance';
                                } else if (inputRadioWithoutInsurance && inputRadioWithoutInsurance.checked) {
                                    lastChoice[productId] = 'without_insurance';
                                }
                                if (inputRadioPayType && inputRadioPayType.checked) {
                                     lastChoice['pay_type_' + productId] = 'from_loan';
                                }
                                insuranceBox.classList.add('d-none');
                            }
                        });
                    }
                });
            });
        </script>
    @endpush
@endonce
