<!DOCTYPE html>
<html lang="{{app()->getLocale()}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    @php
        $seo = $seoDataService->getSeoData();
    @endphp

    <title>{{$seo['title'] ?? __('Stikcredit')}}</title>
    <meta name="title" content="{{$seo['title']??''}}">
    <meta name="description" content="{{$seo['description']??''}}">
    <meta name="keyword" content="{{$seo['keyword']??''}}">
    <meta name="og:url" content="{{$seo['og:url']??''}}">
    <meta name="og:title" content="{{$seo['og:title']??''}}">
    <meta name="og:description" content="{{$seo['og:description']??''}}">
    <meta name="og:type" content="{{$seo['og:type']??''}}">
    <meta name="og:image" content="{{$seo['og:image']??''}}">
    <meta name="og:site_name" content="{{$seo['og:site_name']??''}}">

    <link rel="shortcut icon" type="image/x-icon" href="{{asset('/images/favicon.ico')}}">

    <link rel="stylesheet" href="{{mix('/css/app.css')}}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <x-gtm-head/>

    <!-- Hotjar Tracking Code for stikcredit.bg -->
    <script defer> (function (h, o, t, j, a, r) {
            h.hj = h.hj || function () {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {hjid: 1344785, hjsv: 6};
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv='); </script>
</head>
<body>

<x-gtm-body/>
<x-livechat-source/>

@if(!request()->routeIs('contract'))
    <x-header-sidebar/>
@endif

<main class="container mt-15px mt-lg-5">
    @yield('content')
</main>
<!-- End ./main -->
@yield('outside-container')

@if(!auth()->check() && !request()->routeIs(['login.sms']))
    <x-footer/>
@endif

<script src="{{mix('/js/manifest.js')}}"></script>
<script src="{{mix('/js/vendor.js')}}"></script>
<script src="{{mix('/js/app.js')}}"></script>
<script>
    $(document).on('click', '.disable-on-click', function (e) {
        e.preventDefault();
        $(this).addClass('disabled');
        window.location.replace($(this).attr('href'));
    });

    function disableSubmitButton(form) {
        let buttons = form.querySelectorAll('button[type="submit"]');
        buttons.forEach(button => {
            button.disabled = true;
        });
    }

    function enableSubmitButton(form) {
        let buttons = form.querySelectorAll('button[type="submit"]');
        buttons.forEach(button => {
            if (button) {
                button.disabled = false;
            }
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        const forms = document.querySelectorAll("form");

        forms.forEach(form => {
            form.addEventListener("submit", function (e) {

                if ($(form).is('[data-parsley-validate]') && !$(form).parsley().isValid()) {
                    enableSubmitButton(form); // Re-enable just in case
                    return;
                }

                if (!form.checkValidity()) {
                    enableSubmitButton(form); // Re-enable just in case
                    return;
                }

                disableSubmitButton(form);
            });
        });
    });
</script>
@stack('scripts')
<!-- If user is authenticated init laravel echo listeners -->
@if(auth()->check())
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>
    <script>
        var pusher = new Pusher('{{env('PUSHER_APP_KEY')}}', {
            cluster: 'eu'
        });
        var channel = pusher.subscribe("loan-events.{{auth()->id()}}");
        channel.bind('LoanStatusWasChanged', function(data) {
            window.RedirectManager.redirectToSignLoan(data);
        });

        var clientEvents = pusher.subscribe("client-events.{{auth()->id()}}");
        clientEvents.bind('ClientProfileDataWasUpdated', function (data) {
            window.FetchClientData.refreshClientData(data);
        });
    </script>
@endif
</body>
</html>
