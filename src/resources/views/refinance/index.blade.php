@extends('layout.master')
@section('content')
    <div class="page-container">
        <!-- Calculator -->
        <div class="row">
            <div class="col-12">
                <div class="row">
                    <div class="col-12 col-md-6 offset-md-3">
                        <div id="sliderContent">
                            <ul class="nav nav-tabs justify-content-between nav-justified">
                                @foreach($products as $productId => $productData)
                                    <li class="nav-item">
                                        <a class="nav-link nav-tab-rounded @if($loop->first) active @endif"
                                           data-bs-toggle="tab"
                                           data-bs-target="#product-{{ $productId }}"
                                           href="#product-{{ $productId }}"
                                        >
                                            @if($loop->first)
                                                <h1 class="mb-0 fw-bold fs-6 text-dark">{{ $productData['trade_name'] }}</h1>
                                            @else
                                                <h2 class="mb-0 fw-bold fs-6 text-dark">{{ $productData['trade_name'] }}</h2>
                                            @endif
                                            <small class="text-dark">{{$productData['description']}}</small>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>

                            <div class="tab-content shadow-thick rounded-bottom p-3">
                                {{ csrf_field() }}
                                @php
                                    $isLoggedIn = !empty(Auth::user()?->get('client_id'));
                                @endphp
                                @foreach($products as $productId => $product)
                                    @if(!$isLoggedIn && $productData['productGroup'] == 'payday')
                                        <div class="tab-pane container nav-content-rounded mt-3 @if($loop->first) active @endif"
                                             id="product-{{ $productId }}"
                                        >
                                            <x-slider
                                                :sliderData="$product['slider_data']"
                                                :product="$product"
                                            />

                                            <x-insurance-message
                                                product-id="{{$product['product_id']}}"
                                                :product-data="$productData"
                                            />
                                        </div>
                                    @else
                                        <div class="tab-pane container nav-content-rounded mt-3 @if($loop->first) active @endif"
                                             id="product-{{ $productId }}">
                                            <x-slider
                                                :sliderData="$product['slider_data']"
                                                :hasPeriod="true"
                                                :product="$product"
                                            />

                                            <x-insurance-message
                                                product-id="{{$product['product_id']}}"
                                                :product-data="$productData"
                                            />
                                        </div>
                                    @endif
                                @endforeach

                                <div class="mt-3 js-hidden" id="credit-info">

                                    <div class="alert alert-danger alert-dismissible fade show p-2 d-none" role="alert">
                                        <h5 class="alert-heading">{{__('Нещо не е наред!')}}</h5>
                                        <p class="mb-0 error-message"></p>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>

                                    <x-credit-info
                                            :creditInfo="$creditInfo"
                                            :header="$header"
                                            :isFull="'true'"
                                    />
                                </div>

                                <div class="d-flex justify-content-center align-items-center mt-30px">
                                    @include('partial.payment', ['iban' => $loan_data['iban']])
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-4 mb-4">
        <div class="col-12">
            <div class="row">
                <div class="col-12 col-md-6 offset-md-3">
                    <div class="d-flex flex-column justify-content-center align-items-center">

                        <x-document-download
                                :templateType="'sef'"
                                :templateText="'Изтегли СЕФ'"
                                :loanData="$loan_data"
                        />

                        <div class="w-100 d-block text-center">
                            <button
                                    disabled="disabled"
                                    type="button"
                                    id="refinance-submit"
                                    class="btn w-100 mw-250 text-white rounded-pill btn-pink">
                                {{__('Вземи парите')}}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        window.refinanceSettings = {
            currentAmount: {{$activeLoansAmount/100}},
        };
    </script>
@endpush
