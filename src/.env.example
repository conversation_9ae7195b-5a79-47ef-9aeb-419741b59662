APP_NAME=lendivo_website
APP_ENV=local
APP_KEY=base64:ejzjZoZWQ1Rp8wcGnA0RbYrUDYm8JGe45+xzlHeUGHU=
APP_DEBUG=true
APP_URL=http://localhost:8080
APP_USER=dockerUser
APP_USER_ID=1000
APP_TZ=Europe/Sofia

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_STORE=redis
SESSION_CONNECTION=session

REDIS_HOST=redis
REDIS_CLIENT=phpredis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_WRITE_TIMEOUT=15

MEMCACHED_HOST=127.0.0.1

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

#important for local testing! hostname -I | cut -d' ' -f1
CH_API_ADDRESS=http://************:8000/api/v1
CH_API_TOKEN=a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3
TOKEN=a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3

USE_VERIFF=true
MANDATORY_VERIFICATION=true
REFFER_FRIEND=true

NGINX_PORT=8080
NGINX_PORT_SSL=4431
