<?php

namespace App\Http\Controllers;

use App\Application\Actions\RegisterRequestAction;
use App\Http\Requests\RegisterRequest;
use Illuminate\Http\RedirectResponse;

class RegisterRequestController extends Controller
{
    public function __invoke(
        RegisterRequest $registerRequest,
        RegisterRequestAction $registerRequestAction
    ): RedirectResponse {

        $data = $registerRequest->validated();

        $data['ip'] = $registerRequest->getClientIp();
        $data['browser'] = $registerRequest->userAgent();
        $data['amount_requested'] = floatToInt($data['amount_requested']);
        $data['insurance'] = (isset($data['insurance']) && $data['insurance'] === 'with_insurance') ? 1 : 0;
        if (empty($data['period_requested'])) {
            $data['period_requested'] = 30;
        }

        $response = $registerRequestAction->execute($data);
        if (!isset($response['success']) || !$response['success']) {
            return back()->with('error', $response['message_for_client'] ?? 'Error register request');
        }

        $sessionData = $response['response']['data'];
        $sessionData['request_id'] = $response['response']['request_id'];
        $sessionData['step'] = 2;

        $registerRequest->session()->put($sessionData);

        return to_route('personal.data');
    }
}
