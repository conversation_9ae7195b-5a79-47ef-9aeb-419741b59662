<?php

namespace App\Http\Controllers;

use App\Application\Actions\ProductsDataAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Application\Enums\LoanStatusEnum;
use App\Http\Requests\RefreshLoanStatusRequest;
use App\Http\Requests\SignContractRequest;
use App\Services\ChHelperServiceProvider;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ContractController extends Controller
{
    public function index(Request $request): View|RedirectResponse
    {
        // $loan_data = Auth::user()->get('loan_data');
        $loanId = Auth::user()->get('loan_data')['loan_id'] ?? 0;
        $loan_data = $this->refreshLoanData($loanId, [
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent(),
        ]);

        if (
            !isset($loan_data['installments'][0])
            || $loan_data['loan_status_id'] != LoanStatusEnum::New->id()
        ) {
            return redirect()->route('profile');
        }

        $data['creditInfo'] = [
            'Сума' => $loan_data['amount_approved'] . ' лв. / €' . $loan_data['amount_approved_eur'],
            'Срок' => $loan_data['period_approved'] . ' ' . $loan_data['periodLabel'] ?? '',
            'Брой вноски' => count($loan_data['installments']) . ' вноски',
            'Размер на вноската' => $loan_data['installments'][0]['total_amount'] . ' лв. / €' . $loan_data['installments'][0]['total_amount_eur'],
        ];

        if (!empty($loan_data['insurance_amount']) && intval($loan_data['insurance_amount']) > 0) {
            $data['creditInfo']['Застраховка'] = $loan_data['insurance_amount'] . ' лв. / €' . $loan_data['insurance_amount_eur'];
        }

        /// for affiliate loans show, it will be hide with JS
        if ($loan_data['source'] === 'affiliate') {
            $data['creditInfo']['Застраховка'] = $loan_data['insurance_amount'] . ' лв. / €' . $loan_data['insurance_amount_eur'];
        }

        $data['header'] = __('Информация за кредит');
        $data['client'] = auth()->user();
        $data['loan_data'] = $loan_data;
        $data['iban'] = $loan_data['iban'] ?? null;
        $data['payment_method_id'] = $loan_data['payment_method_id'] ?? null;
        $data['payment_methods'] = app(ProductsDataAction::class)->execute()['payment_methods'];
        $data['showInsuranceOptions'] = false;
        if (
            !empty($loan_data['source']) &&
            $loan_data['source'] === 'affiliate' &&
            $loan_data['amount_approved'] >= config('app.insurance_start_amount')
        ) {
            $data['showInsuranceOptions'] = true;
        }

        return view('contract.index', $data);
    }

    public function getSigned(Request $request): View|RedirectResponse
    {
        $loan_data = Auth::user()->get('loan_data');
        $data['clientId'] = $request->user()?->get('client_id');
        $period = '';
        if ($loan_data['product_type_id'] == 1) {
            $period = 'дни';
            if ($loan_data['period_approved'] % 10 === 1) {
                $period = 'ден';
            }
        } else {
            $period = 'месеца';
            if ($loan_data['period_approved'] % 10 === 1) {
                $period = 'месец';
            }
        }

        if (!isset($loan_data['installments'][0])) {
            return redirect()->route('profile');
        }

        $data['creditInfo'] = [
            'Сума' => $loan_data['amount_approved'] . ' лв. / €' . $loan_data['amount_approved_eur'],
            'Вноска' => $loan_data['installments'][0]['total_amount'] . ' лв / €' . $loan_data['installments'][0]['total_amount_eur'],
            'Период' => $loan_data['period_approved'] . ' ' . $period,
            'Дата на вноската' => Carbon::parse($loan_data['installments'][0]['due_date'])->format('d.m.Y'),
        ];
        if (!empty($loan_data['insurance_amount'])) {
            $data['creditInfo']['Застраховка'] = $loan_data['insurance_amount'] . ' лв. / €' . $loan_data['insurance_amount_eur'];
        }
        $data['header'] = 'Информация за кредит ' . $loan_data['loan_id'];

        return view('contract.signed', $data);
    }

    public function post(
        SignContractRequest     $request,
        ChHelperServiceProvider $chHelperServiceProvider
    ): RedirectResponse
    {
        $data = $request->validated();
        $data['ip'] = $clientIp = $request->getClientIp();
        $data['browser'] = $clientBrowser = $request->userAgent();
        $insurance = $request->validated('insurance');
        if (!empty($insurance)) {
            $data['insurance'] = $insurance === 'with_insurance' ? 1 : 0;
        }

        $loanId = $data['loan_id'];

        /// check current loan status if is it cancelled,
        /// and if client have active loan refresh session data
        /// set active loan and redirect to home page
        $loanStatus = $this->getLoanStatus($loanId, [
            'ip' => $clientIp,
            'browser' => $clientBrowser
        ]);
        $hasActiveLoan = $chHelperServiceProvider->hasActiveLoan([
            'ip' => $clientIp,
            'browser' => $clientBrowser
        ]);

        if (
            $hasActiveLoan
            && isset($loanStatus['loan_status_id'])
            && ($loanStatus['loan_status_id'] === LoanStatusEnum::Cancelled->id())
        ) {
            $chHelperServiceProvider->setActiveLoan();

            return to_route('home.index');
        }

        $result = app(CurlService::class)->chPost(
            ApiRoutesEnum::SIGN_CONTRACT->getRoute() . "/{$loanId}",
            $data
        );
        $response = $result['response'] ?? [];


        if (empty($result['success'])) {
            if (isset($result['message']) && $result['message'] === 'Unauthenticated.') {
                return to_route('login');
            }

            //// if we have some error refresh loan data
            /// and redirect to signed page or back with error message
            if (isset($result['error'])) {
                $loanData = $this->refreshLoanData($loanId);

                return match ($loanData['loan_status_id']) {
                    LoanStatusEnum::Signed->id() => to_route('signed'),
                    LoanStatusEnum::Approved->id(), LoanStatusEnum::Active->id(), LoanStatusEnum::Cancelled->id()
                    => to_route('active.loan'),
                    default => back()->with('error', $result['error'])
                };
            }

            return back()->with('error', 'General error');
        }

        $user = Auth::user();
        $loanData = $user->get('loan_data');

        $loanData['loan_status_id'] = $result['response']['loan_status_id'];
        $user->add('loan_data', $loanData);

        /// if we has enebled veriff verification
        if (
            config('app.use_veriff') === true &&
            !empty($response['new_client']) &&
            (int)$response['new_client'] === 1 &&
            $response['has_action_verif'] === false
        ) {
            return to_route('verification.index');
        }

        return to_route('signed');
    }

    public function getLoanStats(
        RefreshLoanStatusRequest $request,
        CurlService              $curlService
    ): JsonResponse
    {
        $response = [
            'success' => false,
            'redirectTo' => null,
        ];

        $user = Auth::user();
        $loanId = null;
        $clientId = null;

        if (
            $request->validated('client_id') &&
            $request->validated('loan_id')
        ) {
            $clientId = $request->validated('client_id');
            $loanId = $request->validated('loan_id');
        } else {
            $loanId = $user->get('loan_data')['loan_id'];
            $clientId = $user->get('client_id');
        }

        /// if empty loan id return back
        if (intval($loanId) === 0) {
            return response()->json($response);
        }

        $clientIp = $request->getClientIp();
        $clientBrowser = $request->userAgent();

        $apiResponse = $curlService->chPost(
            ApiRoutesEnum::GET_LOAN_STATUS->getRoute() . "/{$loanId}",
            [
                'login_token' => $user->get('remember_token'),
                'client_id' => $clientId,
                'loan_id' => $loanId,
                'ip' => $clientIp,
                'browser' => $clientBrowser
            ]
        );

        if (!empty($apiResponse['success'])) {
            $loanData = $apiResponse['response']['loan_data'];
            $user->add('loan_data', $loanData);
            $user->add('verified', true);

            $chHelperService = app(ChHelperServiceProvider::class);
            $hasActiveLoan = $chHelperService->hasActiveLoan([
                'ip' => $clientIp,
                'browser' => $clientBrowser
            ]);
            /// if refinance request is cancelled but client have active loan
            if (
                $hasActiveLoan &&
                !empty($loanData['loan_status_id']) &&
                $loanData['loan_status_id'] === LoanStatusEnum::Cancelled->id()
            ) {

                /// reset data to active loan
                $chHelperService->setActiveLoan();
            }

            $response['success'] = true;
            try {
                $response['redirectTo'] = match ($loanData['loan_status_id']) {
                    LoanStatusEnum::New->id() => route('contract'),
                    LoanStatusEnum::Signed->id(), LoanStatusEnum::Processing->id() => route('signed'),
                    4, LoanStatusEnum::Approved->id(), LoanStatusEnum::Active->id(), LoanStatusEnum::Cancelled->id()
                    => route('active.loan'), // 4 - ???
                    default => route('profile'),
                };
            } catch (\Throwable $e) {
                $response['success'] = false;
                $response['redirectTo'] = null;

                \Log::error('Error getLoanStats(): ' . $e->getMessage() . ', loan_data' . (!empty($loanData) ? json_encode($loanData) : 'none'));
            }
        }

        return response()->json($response);
    }
}
