<?php

namespace App\Http\Controllers;

use App\Application\Actions\PersonalDataFullDataAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\VerifyDataRequest;
use App\Services\ChHelperServiceProvider;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class VerifyController extends Controller
{
    public function get(
        PersonalDataFullDataAction $dataAction,
        CurlService                $curlService,
        ChHelperServiceProvider    $chHelperServiceProvider,
        Request                    $request
    ): View|RedirectResponse {

        $clientIp = $request->getClientIp();
        $clientBrowser = $request->userAgent();
        $data['client'] = Auth::user()->getAll();

        $showIdCardNumber = false;
        if (!isset($data['client']['idcard'])) {
            $showIdCardNumber = true;
        }

        if (isset($data['client']['idcard'])) {
            $validToDate = $data['client']['idcard']['valid_date'];
            $showIdCardNumber = Carbon::parse($validToDate)->lessThan(Carbon::now()->format('Y-m-d'));
        }

        $data['showIdCardNumber'] = $showIdCardNumber;
        $data['monthsMap'] = $dataAction->getMonthsMap();

        /// before show verify page check client data
        /// 1. if no need to set id card number
        /// 2. if no need to set email address
        /// if all this staff is ok just set flag verified and redirect to active loan
        /// but if we do not have active loan and have session data to create new loan create this.
        if (!$showIdCardNumber && !empty($data['client']['email'])) {

            Auth::user()->add('verified', true);

            $sessionData = session()->all();

            $hasActiveLoan = $chHelperServiceProvider->hasActiveLoan(['ip' => $clientIp, 'browser' => $clientBrowser]);
            if (
                !$hasActiveLoan &&
                !empty($sessionData['product_id']) &&
                !empty($sessionData['period']) &&
                !empty($sessionData['payment_method_id'])
            ) {
                $user = Auth::user();

                /// stupid but quick fix
                $amount_requested = 0;
                if (isset($sessionData['totalAmount'])) {
                    $amount_requested = $sessionData['totalAmount'];
                }
                if (isset($sessionData['amount_requested'])) {
                    $amount_requested = $sessionData['amount_requested'];
                }

                $data = [
                    'client_id' => $user->get('client_id'),
                    'product_id' => $sessionData['product_id'],
                    'amount_requested' => floatToInt($amount_requested),
                    'period_requested' => $sessionData['period'] ?? 30,
                    'payment_method_id' => $sessionData['payment_method_id'],
                    'iban' => $sessionData['iban'] ?? null,
                    'login_token' => $user->get('remember_token'),
                    'ip' => $clientIp,
                    'browser' => $clientBrowser,
                ];

                $apiResponse = $curlService->chPost(
                    ApiRoutesEnum::REQUEST_FROM_PROFILE->getRoute(),
                    $data
                );

                if (!empty($apiResponse['success'])) {
                    $this->refreshLoanData($apiResponse['response']['loan_id']);

                    $response = [
                        'success' => true,
                        'redirectTo' => route($apiResponse['response']['redirect']['url']),
                    ];

                    return redirect()->to($response['redirectTo']);
                }
            }

            return to_route('active.loan');
        }

        return view('verify.index', $data);
    }


    public function post(
        VerifyDataRequest       $request,
        CurlService             $curlService,
        ChHelperServiceProvider $chHelperServiceProvider
    ): RedirectResponse
    {
        $data = $request->validated();

        $sessionData = $request->session()->all();
        $hasActiveLoan = $chHelperServiceProvider->hasActiveLoan([
            'ip' => $request->getClientIp(),
            'browser' => $request->userAgent(),
        ]);
        
        if (
            !$hasActiveLoan &&
            isset($sessionData['product_id']) &&
            !$request->has('idcard_number')
        ) {
            $user = Auth::user();

            /// stupid but quick fix
            $amount_requested = 0;
            if (isset($sessionData['totalAmount'])) {
                $amount_requested = $sessionData['totalAmount'];
            }
            if (isset($sessionData['amount_requested'])) {
                $amount_requested = $sessionData['amount_requested'];
            }

            $data = [
                'client_id' => $user->get('client_id'),
                'product_id' => $sessionData['product_id'],
                'amount_requested' => floatToInt($amount_requested),
                'period_requested' => $sessionData['period'] ?? 30,
                'payment_method_id' => $sessionData['payment_method_id'],
                'iban' => $sessionData['iban'] ?? null,
                'login_token' => $user->get('remember_token'),
                'ip' => $request->getClientIp(),
                'browser' => $request->userAgent(),
            ];

            $apiResp = $curlService->chPost(
                ApiRoutesEnum::REQUEST_FROM_PROFILE->getRoute(),
                $data
            );

            if (!empty($apiResp['success'])) {
                $this->refreshLoanData($apiResp['response']['loan_id']);

                $response = [
                    'success' => true,
                    'redirectTo' => route($apiResp['response']['redirect']['url']),
                ];

                return redirect()->to($response['redirectTo']);
            }
        }

        $user = Auth::user();
        $data['login_token'] = $user->get('remember_token');
        $data['verified'] = true;

        $updateProfile = ($request->get('email') || $request->get('idcard_number'));
        $verifyRequest = $curlService->chPost(ApiRoutesEnum::VERIFY_CLIENT->getRoute(), $data);
        if (empty($verifyRequest['success'])) {
            throw new \Exception('General Error.');
        }

        if (!$updateProfile) {
            $user->add('verified', true);
            return to_route('active.loan');
        }

        $apiResp = [];
        if ($updateProfile) {
            $apiResp = $curlService->chPost(
                ApiRoutesEnum::UPDATE_PROFILE->getRoute(),
                $data
            );
        }

        if (!empty($apiResp['success']) && !$request->get('idcard_number')) {
            $this->refreshClientData($data['client_id']);

            return to_route('active.loan');
        }

        if ($request->get('idcard_number') && intval($apiResp['response']['mvrReportId'] ?? 0) === 0) {
            Session::put('enter_id_card_details', true);

            if (!empty($sessionData['product_id'])) {
                /// stupid but quick fix
                $amount_requested = 0;
                if (isset($sessionData['totalAmount'])) {
                    $amount_requested = $sessionData['totalAmount'];
                }
                if (isset($sessionData['amount_requested'])) {
                    $amount_requested = $sessionData['amount_requested'];
                }

                $request->session()->put([
                    'client_id' => $user->get('client_id'),
                    'product_id' => $sessionData['product_id'],
                    'amount_requested' => floatToInt($amount_requested),
                    'period_requested' => $sessionData['period'] ?? 30,
                    'payment_method_id' => $sessionData['payment_method_id'],
                    'iban' => $sessionData['iban'] ?? null,
                    'login_token' => $user->get('remember_token'),
                ]);
            }

            return to_route('id-card-data.index');
        }


        $this->refreshClientData($data['client_id']);

        return to_route('active.loan');
    }
}
