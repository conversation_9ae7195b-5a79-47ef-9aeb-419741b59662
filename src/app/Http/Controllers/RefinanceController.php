<?php

namespace App\Http\Controllers;

use App\Application\Actions\ProductsDataAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Application\Enums\LoanStatusEnum;
use App\Http\Requests\RefinanceCalcRequest;
use App\Services\CurlService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Contracts\View\View;

class RefinanceController extends Controller
{
    public function index(
        ProductsDataAction $productsDataAction
    ): View|RedirectResponse {

        $user = Auth::user();
        $loanId = $user->get('loan_data')['loan_id'] ?? 0;
        $loanData = !empty($loanId) ? $this->refreshLoanData($loanId) : [];

        if (
            empty($loanData['loan_status_id'])
            || $loanData['loan_status_id'] != LoanStatusEnum::Active->id()
        ) {
            return redirect()->route('signed');
        }

        $activeLoansAmount = $this->getActiveLoansAmount();
        $data = $productsDataAction->execute($user->get('client_id'));

        $data['activeLoansAmount'] = $activeLoansAmount;
        $data['creditInfo'] = [
            'Сума по текущ кредит' => '<span>' . $loanData['total_rest_amount_early'] . '</span> лв. / €' . $loanData['total_rest_amount_early_eur'],
            'Сума по нов кредит' => '<span class="js-dynamic new-loan-amount">0</span> лв. / €<span class="js-dynamic new-loan-amount-eur">0</span>',
            'Сума за получаване' => '<span class="js-dynamic amount-to-receive">0</span> лв. / €<span class="js-dynamic amount-to-receive-eur">0</span>',
            'Дата на първа вноска' => '<span class="js-dynamic new-installment-date">' . Carbon::parse($loanData['installments'][0]['due_date'])->format('d.m.Y') . '</span> г.',
            'Сума на вноската' => '<span class="js-dynamic installment-amount">' . $loanData['installments'][0]['total_amount'] . '</span> лв. / €<span class="js-dynamic installment-amount-eur">' . $loanData['installments'][0]['total_amount_eur'] . '</span>',
            'Период на кредита' => '<span class="js-dynamic period-label">' . $loanData['period_approved'] . ' ' . $loanData['periodLabel'] . '</span>',
        ];

        if (!empty($loanData['insurance_amount'])) {
            $data['creditInfo']['Застраховка'] = $loanData['static_insurance_amount'] . ' лв. / €' . $loanData['static_insurance_amount_eur'];
        }

        $data['header'] = '';
        $data['loan_data'] = $loanData;
        $data['loan_data']['iban'] = $data['loan_data']['iban'] ?? '';

        return view('refinance.index', $data);
    }

    // TODO: refactor
    // has twin: CalculationsController::calcRefinance()
    public function refinanceCalc(RefinanceCalcRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = Auth::user();

        $data['ip'] = $request->getClientIp();
        $data['browser'] = $request->userAgent();
        $data['amount'] = floatToInt($data['amount']);
        $data['client_id'] = $user->get('client_id');
        $data['login_token'] = $user->get('remember_token');
        if (!$data['period']) {
            $data['period'] = 30;
        }

        $apiResponse = app(CurlService::class)->chPost(
            ApiRoutesEnum::REFINANCE_STATS->getRoute(),
            $data
        );

        return response()->json([
            'success' => true,
            'response' => [
                'amount' => $apiResponse['response']['amount'] ?? 0,
                'amount_repaid' => $apiResponse['response']['amount_repaid'] ?? 0,
                'amount_received' => $apiResponse['response']['amount_received'] ?? 0,
                'installment_amount' => $apiResponse['response']['installment_amount'] ?? 0,

                'amount_eur' => $apiResponse['response']['amount_eur'] ?? 0,
                'amount_repaid_eur' => $apiResponse['response']['amount_repaid_eur'] ?? 0,
                'amount_received_eur' => $apiResponse['response']['amount_received_eur'] ?? 0,
                'installment_amount_eur' => $apiResponse['response']['installment_amount_eur'] ?? 0,

                'installment_due_date' => $apiResponse['response']['installment_due_date'] ?? '',
                'period' => $apiResponse['response']['period'] ?? 0,
                'period_label' => $apiResponse['response']['period_label'] ?? '',
            ],
        ]);
    }
}
