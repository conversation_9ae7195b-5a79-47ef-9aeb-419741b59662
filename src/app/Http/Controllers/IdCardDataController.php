<?php

namespace App\Http\Controllers;

use App\Application\Actions\PersonalDataFullDataAction;
use App\Application\Enums\ApiRoutesEnum;
use App\Http\Requests\IdCardDataRequest;
use App\Services\CurlService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class IdCardDataController extends Controller
{

    public function index(
        PersonalDataFullDataAction $dataAction
    ): View|RedirectResponse
    {
        if (!Session::get('enter_id_card_details', false)) {
            return to_route('home.index');
        }

        $data['monthsMap'] = $dataAction->getMonthsMap();
        $data['cities'] = $dataAction->getCities();

        return view('id-card-data.index', $data);
    }


    public function store(IdCardDataRequest $request)
    {
        $user = Auth::user();
        $data = $request->validated();
        $data['login_token'] = $user->get('remember_token');
        $data['client_id'] = $user->get('client_id');
        $data['ip'] = $clientIp = $request->getClientIp();
        $data['browser'] = $clientBrowser = $request->userAgent();

        $apiResp = app(CurlService::class)->chPost(ApiRoutesEnum::UPDATE_ID_CARD->getRoute(), $data);

        $vars = session()->all();

        if (!empty($vars['product_id']) && !empty($apiResp['success']) && !empty($apiResp['response']['client'])) {
            $data = [
                'client_id' => $user->get('client_id'),
                'product_id' => $vars['product_id'],
                'amount_requested' => $vars['amount_requested'],
                'period_requested' => $vars['period_requested'] ?? 30,
                'payment_method_id' => $vars['payment_method_id'],
                'iban' => $vars['iban'] ?? null,
                'login_token' => $user->get('remember_token'),
                'ip' => $clientIp,
                'browser' => $clientBrowser,
            ];

            $newLoanResp = app(CurlService::class)->chPost(
                ApiRoutesEnum::REQUEST_FROM_PROFILE->getRoute(),
                $data
            );

            if (!empty($newLoanResp['success'])) {
                $this->refreshLoanData($newLoanResp['response']['loan_id']);

                $response = [
                    'success' => true,
                    'redirectTo' => route($newLoanResp['response']['redirect']['url']),
                ];

                if ($request->ajax()) {
                    return response()->json($response);
                }
                return redirect()->to($response['redirectTo']);
            }
        }


        if (!empty($apiResp['success']) && !empty($apiResp['response']['client'])) {
            $this->refreshClientData($apiResp['response']['client']['client_id']);
            Session::remove('enter_id_card_details');

            return to_route('active.loan');
        }

        return back()->with('error', 'General error.');
    }
}
