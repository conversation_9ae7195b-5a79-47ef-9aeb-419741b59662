<?php

namespace App\Http\Controllers;

use App\Application\Actions\ProductsDataAction;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Requests\UpdateProfileSettingsRequest;
use App\Http\Requests\EmailUpdateRequest;

class ProfileController extends Controller
{
    public function index(
        ProductsDataAction $productsDataAction
    ): View {

        $user = Auth::user();
        $data = $productsDataAction->execute($user->get('client_id'));
        $data['iban'] = $user->get('iban') ?? false;
        $data['loan_data'] = $user->get('loan_data');

        return view('profile.index', $data);
    }
}
