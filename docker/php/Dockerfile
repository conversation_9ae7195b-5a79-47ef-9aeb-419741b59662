FROM php:8.2-fpm

# Arguments defined in docker-compose.yml
ARG NODE_VERSION=16
ARG user
ARG uid
ENV TZ=Europe/Sofia

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    sudo \
    zip \
    unzip \
    openssl \
    libbz2-dev \
    libicu-dev \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libssl-dev \
    libzip-dev \
    zlib1g-dev \
    libjpeg-dev \
    libmcrypt-dev \
    libreadline-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    g++

RUN apt install --assume-yes nano

RUN curl -sL https://deb.nodesource.com/setup_${NODE_VERSION}.x  | bash -
RUN apt-get -y install nodejs
RUN apt-get -y install npm

RUN npm install -g laravel-echo-server

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install exif pcntl gd \
    bz2 \
    intl \
    iconv \
    bcmath \
    opcache \
    calendar \
    mbstring \
    pdo_mysql \
    zip

RUN apt-get update && apt-get install -y \
    libpq-dev && docker-php-ext-install pdo pdo_pgsql

RUN apt-get update -y \
  && apt-get install -y \
     libxml2-dev \
  && apt-get clean -y \
  && docker-php-ext-install soap

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create system user to run Composer and Artisan Commands
RUN useradd -G www-data,root -u $uid -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R $user:$user /home/<USER>

COPY docker/php/php.ini /usr/local/etc/php/
RUN echo "Set default timezone - Europe/Sofia"
RUN echo "Europe/Sofia" > /etc/timezone

# Set working directory
WORKDIR /var/www

USER $user

EXPOSE 9000
